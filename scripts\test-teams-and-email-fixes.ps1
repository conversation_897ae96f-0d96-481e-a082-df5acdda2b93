# Test Teams Response and Email Notification Fixes
# ===============================================
#
# This script tests the fixes for:
# 1. Teams Logic App response handling for Bug-748404
# 2. Email notification sending
#
# Usage:
#   .\test-teams-and-email-fixes.ps1 -FunctionAppUrl "https://your-function-app.azurewebsites.net" -FunctionKey "your-function-key"

param(
    [Parameter(Mandatory=$true)]
    [string]$FunctionAppUrl,
    
    [string]$FunctionKey = "",
    [string]$TestWorkItemId = "748404",
    [switch]$TestEmailOnly,
    [switch]$TestTeamsOnly,
    [switch]$Verbose
)

function Write-TestHeader {
    param([string]$Title, [string]$Icon = "🧪")
    
    Write-Host ""
    Write-Host "$Icon $Title" -ForegroundColor Cyan
    Write-Host ("=" * 60) -ForegroundColor Gray
}

function Write-TestStep {
    param([string]$Step, [string]$Icon = "🔄")
    
    Write-Host ""
    Write-Host "$Icon $Step" -ForegroundColor Yellow
    Write-Host ("-" * 40) -ForegroundColor Gray
}

function Write-Success {
    param([string]$Message)
    Write-Host "✅ $Message" -ForegroundColor Green
}

function Write-Error {
    param([string]$Message)
    Write-Host "❌ $Message" -ForegroundColor Red
}

function Write-Info {
    param([string]$Message)
    Write-Host "ℹ️  $Message" -ForegroundColor Blue
}

function Test-FunctionAppEndpoint {
    param([string]$Url, [string]$Key)
    
    try {
        $headers = @{
            "Content-Type" = "application/json"
        }
        
        if ($Key) {
            $headers["x-functions-key"] = $Key
        }
        
        $response = Invoke-RestMethod -Uri "$Url/api/health" -Method GET -Headers $headers -TimeoutSec 10
        return $true
    } catch {
        return $false
    }
}

function Test-TeamsResponseHandling {
    param([string]$FunctionUrl, [string]$Key, [string]$WorkItemId)
    
    Write-TestStep "Testing Teams Response Handling" "📱"
    
    # Test 1: Trigger step-by-step workflow for Bug-748404
    Write-Info "Step 1: Triggering workflow for work item $WorkItemId"
    
    try {
        $headers = @{
            "Content-Type" = "application/json"
        }
        
        if ($Key) {
            $headers["x-functions-key"] = $Key
        }
        
        $payload = @{
            work_item_id = [int]$WorkItemId
        } | ConvertTo-Json
        
        $response = Invoke-RestMethod -Uri "$FunctionUrl/api/step_by_step_workflow" -Method POST -Headers $headers -Body $payload -TimeoutSec 60
        
        if ($response.status -eq "success") {
            Write-Success "Workflow triggered successfully"
            Write-Info "Teams notification should be sent to Logic App"
            Write-Info "Check Teams channel for adaptive card"
        } else {
            Write-Error "Workflow failed: $($response.message)"
            return $false
        }
    } catch {
        Write-Error "Failed to trigger workflow: $_"
        return $false
    }
    
    # Test 2: Simulate Logic App response
    Write-Info "Step 2: Simulating Teams Logic App response"
    
    try {
        $responsePayload = @{
            work_item_id = [int]$WorkItemId
            replyText = "Test response from automated test"
            priority = "3"
            action = "quick_accept"
            user_email = "<EMAIL>"
            user_name = "Test User"
            response_timestamp = (Get-Date).ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ssZ")
        } | ConvertTo-Json
        
        $response = Invoke-RestMethod -Uri "$FunctionUrl/api/process_logic_app_response?work_item_id=$WorkItemId" -Method POST -Headers $headers -Body $responsePayload -TimeoutSec 30
        
        if ($response.status -eq "success") {
            Write-Success "Logic App response processed successfully"
            Write-Info "Work item $WorkItemId should be updated in Azure DevOps"
        } else {
            Write-Error "Response processing failed: $($response.message)"
            return $false
        }
    } catch {
        Write-Error "Failed to process Logic App response: $_"
        return $false
    }
    
    Write-Success "Teams response handling test completed"
    return $true
}

function Test-EmailNotifications {
    param([string]$FunctionUrl, [string]$Key, [string]$WorkItemId)
    
    Write-TestStep "Testing Email Notifications" "📧"
    
    # Test 1: Trigger workitem_created to send email
    Write-Info "Step 1: Triggering workitem_created for email notification"
    
    try {
        $headers = @{
            "Content-Type" = "application/json"
        }
        
        if ($Key) {
            $headers["x-functions-key"] = $Key
        }
        
        $payload = @{
            id = [int]$WorkItemId
            eventType = "workitem.created"
            resource = @{
                id = [int]$WorkItemId
                fields = @{
                    "System.Title" = "Test email notification for Bug-$WorkItemId"
                    "System.WorkItemType" = "Bug"
                    "System.State" = "New"
                    "Microsoft.VSTS.Common.Priority" = 3
                }
            }
        } | ConvertTo-Json -Depth 5
        
        $response = Invoke-RestMethod -Uri "$FunctionUrl/api/workitem_created" -Method POST -Headers $headers -Body $payload -TimeoutSec 60
        
        if ($response.status -eq "success") {
            Write-Success "Work item processing completed"
            Write-Info "Email notification should be sent via Logic App"
            Write-Info "Check recipient email for notification"
        } else {
            Write-Error "Work item processing failed: $($response.message)"
            return $false
        }
    } catch {
        Write-Error "Failed to trigger workitem_created: $_"
        return $false
    }
    
    Write-Success "Email notification test completed"
    return $true
}

# Main execution
Write-TestHeader "Teams Response and Email Notification Tests" "🚀"

Write-Info "Function App URL: $FunctionAppUrl"
Write-Info "Test Work Item ID: $TestWorkItemId"
Write-Info "Function Key: $(if ($FunctionKey) { "***provided***" } else { "not provided" })"

# Test Function App connectivity
Write-TestStep "Testing Function App Connectivity" "🔗"

if (Test-FunctionAppEndpoint -Url $FunctionAppUrl -Key $FunctionKey) {
    Write-Success "Function App is accessible"
} else {
    Write-Error "Function App is not accessible. Check URL and key."
    exit 1
}

$allTestsPassed = $true

# Run Teams tests
if (-not $TestEmailOnly) {
    $teamsResult = Test-TeamsResponseHandling -FunctionUrl $FunctionAppUrl -Key $FunctionKey -WorkItemId $TestWorkItemId
    $allTestsPassed = $allTestsPassed -and $teamsResult
}

# Run Email tests
if (-not $TestTeamsOnly) {
    $emailResult = Test-EmailNotifications -FunctionUrl $FunctionAppUrl -Key $FunctionKey -WorkItemId $TestWorkItemId
    $allTestsPassed = $allTestsPassed -and $emailResult
}

# Summary
Write-TestHeader "Test Summary" "📊"

if ($allTestsPassed) {
    Write-Success "All tests passed! ✨"
    Write-Info ""
    Write-Info "Next steps:"
    Write-Info "1. Check Teams channel for adaptive card notification"
    Write-Info "2. Verify Bug-$TestWorkItemId is updated in Azure DevOps"
    Write-Info "3. Check email recipient for notification"
    Write-Info "4. Monitor Function App and Logic App logs for any issues"
} else {
    Write-Error "Some tests failed. Check the output above for details."
    Write-Info ""
    Write-Info "Troubleshooting:"
    Write-Info "1. Verify Function App URL and key are correct"
    Write-Info "2. Check Function App logs for errors"
    Write-Info "3. Ensure Logic Apps are deployed and configured"
    Write-Info "4. Verify Azure DevOps PAT token permissions"
    exit 1
}

Write-TestHeader "Test Complete" "🎉"
