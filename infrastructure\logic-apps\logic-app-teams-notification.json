{"$schema": "https://schema.management.azure.com/providers/Microsoft.Logic/schemas/2016-06-01/workflowdefinition.json#", "contentVersion": "1.0.0.0", "parameters": {"$connections": {"defaultValue": {}, "type": "Object"}}, "triggers": {"When_a_HTTP_request_is_received": {"type": "Request", "kind": "Http", "inputs": {"schema": {"type": "object", "properties": {"To": {"type": "string"}, "Subject": {"type": "string"}, "Body": {"type": "string"}, "Attachments": {"type": "string"}, "attachmentName": {"type": "string"}, "work_item_id": {"type": "integer"}, "adaptive_card": {"type": "object"}}}}}}, "actions": {"Initialize_work_item_id": {"type": "InitializeVariable", "inputs": {"variables": [{"name": "work_item_id", "type": "integer", "value": "@triggerBody()?['work_item_id']"}]}, "runAfter": {}}, "Check_if_adaptive_card_provided": {"type": "If", "expression": {"and": [{"not": {"equals": ["@triggerBody()?['adaptive_card']", "@null"]}}]}, "actions": {"Post_adaptive_card_and_wait_for_a_response": {"type": "ApiConnectionWebhook", "inputs": {"host": {"connection": {"name": "@parameters('$connections')['teams']['connectionId']"}}, "path": "/v1.0/teams/@{encodeURIComponent('your-team-id')}/channels/@{encodeURIComponent('your-channel-id')}/messages/awaitresponse", "body": {"messageBody": "@triggerBody()?['adaptive_card']", "updateMessage": "Thanks for your response!", "recipient": {"to": "@triggerBody()?['To']"}}}, "runAfter": {}}, "Process_adaptive_card_response": {"type": "Http", "inputs": {"method": "POST", "uri": "@{parameters('function_app_url')}/api/process_logic_app_response?work_item_id=@{variables('work_item_id')}", "headers": {"Content-Type": "application/json", "x-functions-key": "@parameters('function_app_key')"}, "body": {"work_item_id": "@variables('work_item_id')", "replyText": "@{body('Post_adaptive_card_and_wait_for_a_response')?['data']?['replyText']}", "priority": "@{body('Post_adaptive_card_and_wait_for_a_response')?['data']?['priority']}", "action": "@{body('Post_adaptive_card_and_wait_for_a_response')?['data']?['action']}", "user_email": "@{body('Post_adaptive_card_and_wait_for_a_response')?['responder']?['email']}", "user_name": "@{body('Post_adaptive_card_and_wait_for_a_response')?['responder']?['displayName']}", "response_timestamp": "@utcNow()"}}, "runAfter": {"Post_adaptive_card_and_wait_for_a_response": ["Succeeded"]}}}, "else": {"actions": {"Send_simple_teams_message": {"type": "Http", "inputs": {"method": "POST", "uri": "@parameters('teams_webhook_url')", "headers": {"Content-Type": "application/json"}, "body": {"text": "@triggerBody()?['Body']", "title": "@triggerBody()?['Subject']"}}}}}, "runAfter": {"Initialize_work_item_id": ["Succeeded"]}}, "Response": {"type": "Response", "kind": "Http", "inputs": {"statusCode": 200, "body": {"status": "success", "message": "Teams notification processed", "work_item_id": "@variables('work_item_id')", "timestamp": "@utcNow()"}}, "runAfter": {"Check_if_adaptive_card_provided": ["Succeeded", "Failed"]}}}}