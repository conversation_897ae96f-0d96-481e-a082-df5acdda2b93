# Teams Response and Email Notification Fixes

## Overview

This document describes the fixes implemented to resolve two critical issues:

1. **Teams Logic App Response Handling**: workitem_create now waits for Teams Logic App responses to update Bug-748404
2. **Email Notification Issues**: Fixed email Logic App integration and conditional logic

## Issue 1: Teams Logic App Response Handling

### Problem
The system was sending Teams notifications but not waiting for user responses to update work items like Bug-748404. The response handling infrastructure existed but lacked the proper Teams Logic App workflow.

### Solution
1. **Created Teams Logic App Workflow** (`infrastructure/logic-apps/logic-app-teams-notification.json`)
   - Includes adaptive card posting with `Post_adaptive_card_and_wait_for_a_response` action
   - Waits for user responses and processes them via Function App callback
   - Supports both adaptive cards and simple Teams messages

2. **Updated Teams Client** (`functions/__app__/common/adapters/teams_client.py`)
   - Modified `_send_teams_logic_app_notification()` to send adaptive cards instead of simple text
   - Added `work_item_id` and `adaptive_card` to payload
   - Enhanced logging to track adaptive card notifications

3. **Response Processing Pipeline** (already existed)
   - `teams_response_handler.py` processes Logic App responses
   - `ResponseProcessingPipeline` handles complete response workflow
   - Updates work items in Azure DevOps based on user feedback

### Workflow
```
1. Work item created → AI Triage → Teams Logic App
2. Teams Logic App posts adaptive card and waits for response
3. User responds via adaptive card
4. Logic App calls Function App with response data
5. Function App processes response and updates Bug-748404
```

## Issue 2: Email Notification Problems

### Problem
Email Logic App integration had conditional logic issues and payload structure problems that prevented emails from being sent.

### Solution
1. **Fixed Email Payload Structure**
   - Added `Name` field alongside `ContentBytes` and `attachmentName`
   - Ensured proper conditional logic: empty `attachmentName` + `Attachments: true` = simple email
   - Non-empty `attachmentName` + `Attachments: true` = Virgin Atlantic branded email with attachment

2. **Added Simple Email Method**
   - Created `_send_simple_email_notification()` for fallback scenarios
   - Uses conditional logic to trigger simple email flow in Logic App
   - Provides plain text email without attachments

3. **Enhanced Error Handling**
   - Added fallback from attachment email to simple email on failure
   - Improved logging to distinguish between email types
   - Better error messages for debugging

### Email Logic App Conditional Logic
```
IF attachmentName is empty AND Attachments is true:
    → Send simple email
ELSE:
    → Send Virgin Atlantic branded email with attachment support using ContentBytes and Name fields
```

## Deployment Instructions

### 1. Deploy Teams Logic App
```powershell
cd infrastructure/logic-apps
.\deploy-logic-app-teams.ps1 -ResourceGroupName "rg-autodefecttriage" -LogicAppName "teams-notification-logic-app" -FunctionAppUrl "https://your-function-app.azurewebsites.net" -FunctionAppKey "your-function-key"
```

### 2. Update Function App Configuration
Update `local.settings.json` or Azure Function App settings:
```json
{
  "TEAMS_LOGIC_APP_URL": "https://your-teams-logic-app-url",
  "Email_LOGIC_APP_URL": "https://your-email-logic-app-url"
}
```

### 3. Configure Teams Connection
1. Go to the Teams Logic App in Azure Portal
2. Configure the Teams connection for your organization
3. Update team-id and channel-id in the workflow definition

## Testing Instructions

### Test Teams Response Handling with Bug-748404

1. **Trigger Workflow for Bug-748404**
```bash
curl -X POST "https://your-function-app.azurewebsites.net/api/step_by_step_workflow" \
  -H "Content-Type: application/json" \
  -d '{"work_item_id": 748404}'
```

2. **Verify Teams Adaptive Card**
   - Check Teams channel for adaptive card notification
   - Verify card includes priority selection and feedback options
   - Confirm work item details are displayed correctly

3. **Test User Response**
   - Click on adaptive card buttons (Accept AI Triage, Request Reassignment)
   - Fill in priority dropdown and feedback text
   - Submit response

4. **Verify Work Item Update**
   - Check Bug-748404 in Azure DevOps
   - Verify priority, assignment, and history comments are updated
   - Confirm response processing logs in Function App

### Test Email Notifications

1. **Test Attachment Email**
```bash
curl -X POST "https://your-function-app.azurewebsites.net/api/workitem_created" \
  -H "Content-Type: application/json" \
  -d '{"id": 748404, "eventType": "workitem.created"}'
```

2. **Verify Email Delivery**
   - Check recipient email for Virgin Atlantic branded email
   - Verify attachment is included (DefectAnalysis_748404_Bug.txt)
   - Confirm email content includes work item details

3. **Test Simple Email Fallback**
   - Simulate attachment email failure
   - Verify simple email is sent as fallback
   - Check email content is plain text format

## Monitoring and Troubleshooting

### Key Logs to Monitor
1. **Teams Logic App Logs**
   - Adaptive card posting success/failure
   - Response processing status
   - Function App callback results

2. **Function App Logs**
   - Teams notification sending
   - Response processing pipeline
   - Work item updates
   - Email sending attempts

3. **Email Logic App Logs**
   - Conditional logic execution
   - Attachment processing
   - Email delivery status

### Common Issues and Solutions

1. **Teams Adaptive Card Not Appearing**
   - Check Teams connection configuration
   - Verify team-id and channel-id in Logic App
   - Ensure Function App URL is accessible

2. **Bug-748404 Not Getting Updated**
   - Check response processing pipeline logs
   - Verify ADO PAT token permissions
   - Confirm work item ID in response payload

3. **Emails Not Being Sent**
   - Check Email Logic App conditional logic
   - Verify payload structure (ContentBytes, Name, attachmentName)
   - Test simple email fallback

4. **Response Processing Failures**
   - Check Function App authentication
   - Verify response payload format
   - Monitor ResponseProcessingPipeline logs

## Configuration Reference

### Required Environment Variables
```
TEAMS_LOGIC_APP_URL=https://your-teams-logic-app-url
Email_LOGIC_APP_URL=https://your-email-logic-app-url
ADO_ORGANIZATION=virginatlantic
ADO_PROJECT=Air4 Channels Testing
ADO_PAT_TOKEN=your-ado-pat-token
```

### Teams Logic App Parameters
- `teams_webhook_url`: Fallback webhook for simple messages
- `function_app_url`: Function App URL for response processing
- `function_app_key`: Function App authentication key

### Email Logic App Payload Structure
```json
{
  "Body": "HTML email body",
  "Subject": "Email subject",
  "To": "<EMAIL>",
  "Attachments": true,
  "attachmentName": "filename.txt",
  "ContentBytes": "base64-encoded-content",
  "Name": "filename.txt"
}
```

## Next Steps

1. Deploy the Teams Logic App using provided scripts
2. Update Function App configuration with Logic App URLs
3. Test with Bug-748404 to verify end-to-end functionality
4. Monitor logs for any issues and adjust configuration as needed
5. Consider extending to other test work items once Bug-748404 testing is successful
