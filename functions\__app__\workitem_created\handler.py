"""
Azure Function: WorkItem Created Handler
HTTP trigger for Azure DevOps Service Hook when work items are created/updated.
"""

import json
import logging
from typing import Dict, Any, Optional, List
import azure.functions as func
from azure.functions import HttpRequest, HttpResponse

from ..common.models.schemas import WorkItem, TriageResult
from ..common.adapters.ado_client import AdoClient
from ..common.adapters.search_client import SearchClient
from ..common.adapters.teams_client import TeamsClient
from ..common.ai.duplicate import DuplicateDetector
from ..common.ai.assigner import AssignmentEngine
from ..common.ai.priority import PriorityEngine
from ..common.utils.config import get_config
from ..common.utils.logging import setup_logging, log_structured

# Initialize logging
setup_logging()
logger = logging.getLogger(__name__)

# Initialize clients (will be lazy-loaded)
_ado_client: Optional[AdoClient] = None
_search_client: Optional[SearchClient] = None
_teams_client: Optional[TeamsClient] = None
_duplicate_detector: Optional[DuplicateDetector] = None
_assignment_engine: Optional[AssignmentEngine] = None
_priority_engine: Optional[PriorityEngine] = None


def get_clients():
    """Lazy initialization of clients."""
    global _ado_client, _search_client, _teams_client
    global _duplicate_detector, _assignment_engine, _priority_engine
    
    if _ado_client is None:
        config = get_config()
        _ado_client = AdoClient(config)
        _search_client = SearchClient(config)
        _teams_client = TeamsClient(config)
        _duplicate_detector = DuplicateDetector(_search_client, config)
        _assignment_engine = AssignmentEngine(_search_client, config)
        _priority_engine = PriorityEngine(config)
    
    return {
        'ado': _ado_client,
        'search': _search_client,
        'teams': _teams_client,
        'duplicate': _duplicate_detector,
        'assigner': _assignment_engine,
        'priority': _priority_engine
    }


def extract_work_item_from_webhook(webhook_data: Dict[str, Any]) -> Optional[WorkItem]:
    """
    Extract work item data from ADO webhook payload and convert to WorkItem model.
    """
    try:
        # ADO webhook structure: resource.fields contains the work item data
        resource = webhook_data.get('resource', {})
        fields = resource.get('fields', {})
        
        if not fields:
            logger.warning("No fields found in webhook payload")
            return None
        
        # Extract and map fields to WorkItem model
        work_item_data = {
            'id': resource.get('id') or resource.get('workItemId'),
            'title': fields.get('System.Title', ''),
            'description': fields.get('System.Description', ''),
            'work_item_type': fields.get('System.WorkItemType', ''),
            'state': fields.get('System.State', ''),
            'area_path': fields.get('System.AreaPath', ''),
            'iteration_path': fields.get('System.IterationPath', ''),
            'assigned_to': _extract_user_display_name(fields.get('System.AssignedTo')),
            'created_by': _extract_user_display_name(fields.get('System.CreatedBy')),
            'created_date': fields.get('System.CreatedDate', ''),
            'changed_date': fields.get('System.ChangedDate', ''),
            'priority': fields.get('Microsoft.VSTS.Common.Priority', 2),
            'severity': fields.get('Microsoft.VSTS.Common.Severity', ''),
            'tags': fields.get('System.Tags', ''),
            'repro_steps': fields.get('Microsoft.VSTS.TCM.ReproSteps', ''),
            'system_info': fields.get('Microsoft.VSTS.TCM.SystemInfo', ''),
        }
        
        # Create WorkItem instance
        work_item = WorkItem(**work_item_data)
        
        log_structured(
            logger,
            "info",
            "Extracted work item from webhook",
            extra={
                "work_item_id": work_item.id,
                "work_item_type": work_item.work_item_type,
                "title": work_item.title[:100] + "..." if len(work_item.title) > 100 else work_item.title
            }
        )
        
        return work_item
        
    except Exception as e:
        log_structured(
            logger,
            "error",
            "Failed to extract work item from webhook",
            extra={
                "error": str(e),
                "webhook_keys": list(webhook_data.keys()) if webhook_data else []
            }
        )
        return None


def _extract_user_display_name(user_field: Any) -> str:
    """Extract display name from user field (can be dict or string)."""
    if isinstance(user_field, dict):
        return user_field.get('displayName', '')
    elif isinstance(user_field, str):
        return user_field
    else:
        return ''


async def process_workitem_webhook(req: HttpRequest) -> HttpResponse:
    """
    Main function to process work items - analyzes Bugs/defects from last 24 hours.

    This function:
    1. Queries ADO for all Bug/defect work items created in last 24 hours
    2. Runs AI triage for each (duplicate detection, assignment, priority)
    3. Sends Teams notifications with recommendations (does NOT update work items)
    4. Indexes work items for future searches

    NOTE: Work items are NOT automatically updated. Updates only happen when users
    respond to Teams notifications via the Teams response flow.

    Query parameters:
    - hours_back: Number of hours to look back (default: 24)
    - dry_run: If true, only shows what would be processed without making changes
    """
    try:
        # Get query parameters
        hours_back = int(req.params.get('hours_back', 72))  # Default to 24 hours
        dry_run = req.params.get('dry_run', 'false').lower() == 'true'

        log_structured(
            logger,
            "info",
            "Processing Bug/defect work items from last 24 hours",
            extra={
                "hours_back": hours_back,
                "dry_run": dry_run,
                "method": req.method
            }
        )

        # Initialize clients
        clients = get_clients()

        # Get Bug/defect work items from last 24 hours
        bug_work_items = await get_recent_bug_work_items(clients['ado'], hours_back)

        if not bug_work_items:
            response_data = {
                "status": "success",
                "message": f"No Bug/defect work items found in last {hours_back} hours",
                "work_items_found": 0,
                "work_items_processed": 0,
                "dry_run": dry_run,
                "hours_back": hours_back
            }

            log_structured(
                logger,
                "info",
                f"No Bug work items found in last {hours_back} hours"
            )

            return func.HttpResponse(
                json.dumps(response_data, indent=2),
                status_code=200,
                mimetype="application/json"
            )

        log_structured(
            logger,
            "info",
            f"Found {len(bug_work_items)} Bug/defect work items to process"
        )

        # Process each Bug work item
        processed_items = []
        total_processed = 0

        for work_item_data in bug_work_items:
            try:
                # Convert to WorkItem object
                work_item = convert_ado_to_work_item(work_item_data)

                if dry_run:
                    # Simulate processing
                    log_structured(
                        logger,
                        "info",
                        f"DRY RUN: Would process Bug {work_item.id}",
                        extra={
                            "work_item_id": work_item.id,
                            "title": work_item.title[:50]
                        }
                    )

                    processed_items.append({
                        "work_item_id": work_item.id,
                        "title": work_item.title[:100],
                        "work_item_type": work_item.work_item_type,
                        "state": work_item.state,
                        "dry_run": True,
                        "would_process": True
                    })
                    total_processed += 1
                    continue

                # Run AI triage pipeline
                triage_result = await run_triage_pipeline(work_item, clients)

                # Send Teams notification (do NOT update work item automatically)
                # Work items should only be updated when users respond via Teams
                await send_teams_notification(work_item, triage_result, clients['teams'])

                # Index work item for future similarity searches
                await index_work_item(work_item, clients['search'])

                processed_items.append({
                    "work_item_id": work_item.id,
                    "title": work_item.title[:100],
                    "work_item_type": work_item.work_item_type,
                    "state": work_item.state,
                    "assigned_to": triage_result.assigned_to if triage_result else None,
                    "priority": triage_result.priority if triage_result else None,
                    "duplicates_found": len(triage_result.duplicates) if triage_result and triage_result.duplicates else 0,
                    "confidence_score": triage_result.confidence_score if triage_result else None
                })

                total_processed += 1

                log_structured(
                    logger,
                    "info",
                    f"Successfully processed Bug {work_item.id}",
                    extra={
                        "work_item_id": work_item.id,
                        "assigned_to": triage_result.assigned_to if triage_result else None,
                        "priority": triage_result.priority if triage_result else None
                    }
                )

            except Exception as e:
                log_structured(
                    logger,
                    "error",
                    f"Error processing Bug work item {work_item_data.get('id', 'unknown')}: {e}",
                    extra={"work_item_data": work_item_data},
                    exc_info=True
                )

                processed_items.append({
                    "work_item_id": work_item_data.get('id', 'unknown'),
                    "title": work_item_data.get('fields', {}).get('System.Title', 'Unknown')[:100],
                    "error": str(e),
                    "processed": False
                })
                continue

        # Return success response
        response_data = {
            "status": "success",
            "message": f"Processed {total_processed} Bug/defect work items from last {hours_back} hours",
            "work_items_found": len(bug_work_items),
            "work_items_processed": total_processed,
            "dry_run": dry_run,
            "hours_back": hours_back,
            "processed_items": processed_items
        }

        log_structured(
            logger,
            "info",
            f"Bug processing completed",
            extra={
                "work_items_found": len(bug_work_items),
                "work_items_processed": total_processed,
                "dry_run": dry_run
            }
        )

        return func.HttpResponse(
            json.dumps(response_data, indent=2),
            status_code=200,
            mimetype="application/json"
        )
        
    except Exception as e:
        log_structured(
            logger,
            "error",
            "Unexpected error processing webhook",
            extra={
                "error": str(e),
                "error_type": type(e).__name__
            }
        )
        
        return func.HttpResponse(
            json.dumps({
                "error": "Internal server error",
                "message": str(e)
            }),
            status_code=500,
            mimetype="application/json"
        )


async def get_recent_bug_work_items(ado_client: AdoClient, hours_back: int = 24) -> List[Dict[str, Any]]:
    """Get Bug/defect work items created in the last N hours."""
    try:
        from datetime import datetime, timedelta

        # Calculate the date threshold (date only, no time for WIQL)
        threshold_date = datetime.utcnow() - timedelta(hours=hours_back)
        threshold_str = threshold_date.strftime('%Y-%m-%d')

        # WIQL query to get Bug work items created in the specified time range
        # Using minimal fields that we know work
        wiql_query = f"""
        SELECT [System.Id], [System.Title], [System.WorkItemType], [System.CreatedDate]
        FROM WorkItems
        WHERE [System.WorkItemType] = 'Bug'
        AND [System.CreatedDate] >= '{threshold_str}'
        ORDER BY [System.CreatedDate] DESC
        """

        log_structured(
            logger,
            "info",
            f"Querying for Bug work items created since {threshold_str}",
            extra={"hours_back": hours_back, "threshold": threshold_str}
        )

        # Execute the WIQL query
        work_items = await ado_client.query_work_items(wiql_query)

        log_structured(
            logger,
            "info",
            f"Found {len(work_items)} Bug work items in last {hours_back} hours"
        )

        return work_items

    except Exception as e:
        log_structured(
            logger,
            "error",
            f"Error querying Bug work items: {e}",
            exc_info=True
        )
        return []


def convert_ado_to_work_item(work_item_data: Dict[str, Any]) -> WorkItem:
    """Convert Azure DevOps work item data to WorkItem object."""
    try:
        fields = work_item_data.get("fields", {})

        # Extract basic fields
        work_item_id = work_item_data.get("id")
        title = fields.get("System.Title", "")
        work_item_type = fields.get("System.WorkItemType", "Bug")
        state = fields.get("System.State", "New")

        # Extract description and repro steps
        description = fields.get("System.Description", "")
        repro_steps = fields.get("System.ReproSteps", "")

        # Combine description and repro steps for content
        content_parts = []
        if description:
            content_parts.append(f"Description: {description}")
        if repro_steps:
            content_parts.append(f"Reproduction Steps: {repro_steps}")

        content = "\n\n".join(content_parts) if content_parts else title

        # Extract other fields
        assigned_to = fields.get("System.AssignedTo", {}).get("displayName", "") if isinstance(fields.get("System.AssignedTo"), dict) else fields.get("System.AssignedTo", "")
        priority = fields.get("Microsoft.VSTS.Common.Priority") or fields.get("System.Priority", 2)
        severity = fields.get("Microsoft.VSTS.Common.Severity", "3 - Medium")
        area_path = fields.get("System.AreaPath", "")
        iteration_path = fields.get("System.IterationPath", "")

        # Extract dates
        created_date = fields.get("System.CreatedDate", "")
        changed_date = fields.get("System.ChangedDate", "")

        # Create WorkItem object
        work_item = WorkItem(
            id=work_item_id,
            title=title,
            description=content,  # Use description field instead of content
            work_item_type=work_item_type,
            state=state,
            assigned_to=assigned_to,
            priority=int(priority) if isinstance(priority, (int, str)) and str(priority).isdigit() else 2,
            severity=severity,
            area_path=area_path,
            iteration_path=iteration_path,
            created_date=created_date,
            changed_date=changed_date,
            tags=""  # Tags would need to be extracted separately if needed
        )

        log_structured(
            logger,
            "debug",
            f"Converted ADO work item {work_item_id} to WorkItem object",
            extra={
                "work_item_id": work_item_id,
                "title": title[:50],
                "type": work_item_type,
                "state": state
            }
        )

        return work_item

    except Exception as e:
        log_structured(
            logger,
            "error",
            f"Error converting ADO work item to WorkItem object: {e}",
            extra={"work_item_data": work_item_data},
            exc_info=True
        )
        raise


async def run_triage_pipeline(work_item: WorkItem, clients: Dict[str, Any]) -> Optional[TriageResult]:
    """
    Run the complete AI triage pipeline for a work item.
    """
    try:
        # Step 1: Duplicate Detection
        duplicates = await clients['duplicate'].find_duplicates(work_item)
        
        # Step 2: Priority Analysis
        priority = await clients['priority'].calculate_priority(work_item)
        
        # Step 3: Assignment
        assignment = await clients['assigner'].assign_work_item(work_item)
        
        # Create triage result
        reasoning = assignment.get('reasoning', '')
        if isinstance(reasoning, list):
            reasoning = '; '.join(reasoning)

        triage_result = TriageResult(
            work_item_id=work_item.id,
            assigned_to=assignment.get('assigned_to', ''),
            priority=priority,
            duplicates=[dup.dict() for dup in duplicates] if duplicates else [],
            confidence_score=assignment.get('confidence', 0.0),
            reasoning=reasoning
        )
        
        log_structured(
            logger,
            "info",
            "Completed triage pipeline",
            extra={
                "work_item_id": work_item.id,
                "duplicates_found": len(duplicates) if duplicates else 0,
                "assigned_to": triage_result.assigned_to,
                "priority": triage_result.priority
            }
        )
        
        return triage_result
        
    except Exception as e:
        log_structured(
            logger,
            "error",
            "Error in triage pipeline",
            extra={
                "work_item_id": work_item.id,
                "error": str(e)
            }
        )
        return None


async def update_work_item_with_triage(work_item: WorkItem, triage_result: TriageResult, ado_client: AdoClient):
    """Update the work item in ADO with triage results."""
    try:
        if not triage_result:
            return
        
        updates = {}
        
        # Update assignment if we have a confident assignment
        if triage_result.assigned_to and triage_result.confidence_score > 0.7:
            updates['System.AssignedTo'] = triage_result.assigned_to
        
        # Update priority if it changed
        if triage_result.priority != work_item.priority:
            updates['Microsoft.VSTS.Common.Priority'] = triage_result.priority
        
        # Add triage reasoning as a comment
        if triage_result.reasoning:
            comment = f"🤖 AI Triage: {triage_result.reasoning}"
            if triage_result.duplicates:
                comment += f"\n\n🔍 Potential duplicates found: {len(triage_result.duplicates)}"
            
            updates['System.History'] = comment
        
        if updates:
            await ado_client.update_work_item(work_item.id, updates)
            log_structured(
                logger,
                "info",
                "Updated work item with triage results",
                extra={
                    "work_item_id": work_item.id,
                    "updates": list(updates.keys())
                }
            )
        
    except Exception as e:
        log_structured(
            logger,
            "error",
            "Failed to update work item",
            extra={
                "work_item_id": work_item.id,
                "error": str(e)
            }
        )


async def send_teams_notification(work_item: WorkItem, triage_result: TriageResult, teams_client: TeamsClient):
    """Send notification to Teams channel."""
    try:
        if not triage_result:
            return
        
        await teams_client.send_triage_notification(work_item, triage_result)
        
        log_structured(
            logger,
            "info",
            "Sent Teams notification",
            extra={"work_item_id": work_item.id}
        )
        
    except Exception as e:
        log_structured(
            logger,
            "error",
            "Failed to send Teams notification",
            extra={
                "work_item_id": work_item.id,
                "error": str(e)
            }
        )


async def index_work_item(work_item: WorkItem, search_client: SearchClient):
    """Index the work item for future similarity searches."""
    try:
        await search_client.index_work_item(work_item)
        
        log_structured(
            logger,
            "info",
            "Indexed work item for search",
            extra={"work_item_id": work_item.id}
        )
        
    except Exception as e:
        log_structured(
            logger,
            "error",
            "Failed to index work item",
            extra={
                "work_item_id": work_item.id,
                "error": str(e)
            }
        )
